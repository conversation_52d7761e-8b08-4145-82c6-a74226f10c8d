"""
测试AI和AO任务的集成功能

本测试文件用于测试AI和AO任务的协同工作，验证信号的完整性。
"""

import pytest
import numpy as np
import time
from sweeper400.analyze import init_sampling_info, sine_wave_vvi, extract_single_tone_information_vvi
from sweeper400.measure.draft import finite_ai_single_channel, finite_ao_single_channel


def test_ai_ao_loopback_basic():
    """
    测试基本的AI-AO环回功能
    
    该测试生成一个正弦波，通过AO输出，然后通过AI采集，验证信号的完整性。
    注意：需要手动连接AO和AI通道进行环回测试。
    """
    print("\n=== 测试AI-AO环回功能 ===")
    print("注意：此测试需要手动连接AO和AI通道进行环回")
    
    # 创建测试参数
    sampling_rate = 2000  # 2 kHz
    samples_num = 2048    # 2048个样本
    sampling_info = init_sampling_info(sampling_rate, samples_num)
    
    # 生成测试信号
    test_frequency = 100.0  # 100 Hz
    test_amplitude = 1.0    # 1V幅值
    test_phase = np.pi / 6  # 30度相位
    
    original_waveform = sine_wave_vvi(
        sampling_info, 
        frequency=test_frequency, 
        amplitude=test_amplitude, 
        phase=test_phase
    )
    
    print(f"原始信号: {test_frequency} Hz, {test_amplitude} V, 相位 {test_phase:.4f} rad")
    print(f"原始波形: {original_waveform}")
    
    # 定义测试通道
    ao_channel = "402Dev2Slot2/ao0"
    ai_channel = "402Dev2Slot2/ai0"
    
    try:
        # 步骤1: 输出信号
        print(f"\n步骤1: 通过 {ao_channel} 输出信号...")
        samples_written = finite_ao_single_channel(ao_channel, original_waveform)
        assert samples_written == samples_num
        print(f"✓ 成功输出 {samples_written} 个样本")
        
        # 等待一小段时间确保输出完成
        time.sleep(0.1)
        
        # 步骤2: 采集信号
        print(f"\n步骤2: 通过 {ai_channel} 采集信号...")
        acquired_waveform = finite_ai_single_channel(ai_channel, sampling_info)
        print(f"✓ 成功采集 {acquired_waveform.samples_num} 个样本")
        print(f"采集波形: {acquired_waveform}")
        
        # 步骤3: 分析采集到的信号
        print(f"\n步骤3: 分析采集信号...")
        detected_freq, detected_amp, detected_phase = extract_single_tone_information_vvi(
            acquired_waveform, approx_freq=test_frequency
        )
        
        print(f"检测结果:")
        print(f"  - 频率: {detected_freq:.2f} Hz (原始: {test_frequency:.2f} Hz)")
        print(f"  - 幅值: {detected_amp:.4f} V (原始: {test_amplitude:.4f} V)")
        print(f"  - 相位: {detected_phase:.4f} rad (原始: {test_phase:.4f} rad)")
        
        # 验证结果（允许一定误差）
        freq_error = abs(detected_freq - test_frequency)
        amp_error = abs(detected_amp - test_amplitude)
        
        print(f"\n验证结果:")
        print(f"  - 频率误差: {freq_error:.2f} Hz")
        print(f"  - 幅值误差: {amp_error:.4f} V")
        
        # 频率误差应该很小（< 1 Hz）
        assert freq_error < 1.0, f"频率误差过大: {freq_error:.2f} Hz"
        
        # 幅值误差取决于硬件精度，允许较大误差（< 20%）
        amp_error_percent = (amp_error / test_amplitude) * 100
        assert amp_error_percent < 20.0, f"幅值误差过大: {amp_error_percent:.1f}%"
        
        print(f"✓ 环回测试通过 - 频率误差: {freq_error:.2f} Hz, 幅值误差: {amp_error_percent:.1f}%")
        
    except Exception as e:
        pytest.fail(f"AI-AO环回测试失败: {e}")


def test_ai_ao_different_channels():
    """
    测试不同通道组合的AI-AO功能
    """
    print("\n=== 测试不同通道组合 ===")
    
    # 测试通道组合
    channel_pairs = [
        ("402Dev2Slot2/ao0", "402Dev2Slot2/ai0"),
        ("402Dev2Slot2/ao1", "402Dev2Slot2/ai1"),
        ("402Dev2Slot3/ao0", "402Dev2Slot3/ai0"),
        ("402Dev2Slot3/ao1", "402Dev2Slot3/ai1"),
    ]
    
    sampling_info = init_sampling_info(1000, 1024)
    
    for i, (ao_ch, ai_ch) in enumerate(channel_pairs):
        print(f"\n测试通道对 {i+1}: {ao_ch} -> {ai_ch}")
        
        # 为每个通道对生成不同的测试信号
        frequency = 50.0 + i * 30.0  # 50, 80, 110, 140 Hz
        amplitude = 0.5 + i * 0.3    # 0.5, 0.8, 1.1, 1.4 V
        
        test_waveform = sine_wave_vvi(sampling_info, frequency, amplitude)
        
        try:
            # 输出信号
            samples_written = finite_ao_single_channel(ao_ch, test_waveform)
            assert samples_written == 1024
            
            # 等待输出完成
            time.sleep(0.05)
            
            # 采集信号
            acquired_waveform = finite_ai_single_channel(ai_ch, sampling_info)
            assert acquired_waveform.samples_num == 1024
            
            print(f"✓ 成功 - {frequency} Hz, {amplitude} V")
            
        except Exception as e:
            print(f"✗ 通道对 {ao_ch}->{ai_ch} 测试失败: {e}")
            # 不使用pytest.fail，因为某些通道可能没有物理连接


def test_ai_ao_signal_quality():
    """
    测试信号质量和噪声水平
    """
    print("\n=== 测试信号质量 ===")
    
    sampling_info = init_sampling_info(5000, 4096)  # 高采样率，更多样本
    
    # 生成高质量测试信号
    test_frequency = 250.0  # 250 Hz
    test_amplitude = 2.0    # 2V幅值
    
    test_waveform = sine_wave_vvi(sampling_info, test_frequency, test_amplitude)
    
    ao_channel = "402Dev2Slot2/ao0"
    ai_channel = "402Dev2Slot2/ai0"
    
    try:
        # 输出信号
        samples_written = finite_ao_single_channel(ao_channel, test_waveform)
        print(f"输出 {samples_written} 个样本")
        
        time.sleep(0.1)
        
        # 采集信号
        acquired_waveform = finite_ai_single_channel(ai_channel, sampling_info)
        print(f"采集 {acquired_waveform.samples_num} 个样本")
        
        # 分析信号质量
        detected_freq, detected_amp, detected_phase = extract_single_tone_information_vvi(
            acquired_waveform, approx_freq=test_frequency
        )
        
        # 计算信号统计信息
        signal_mean = np.mean(acquired_waveform)
        signal_std = np.std(acquired_waveform)
        signal_rms = np.sqrt(np.mean(acquired_waveform**2))
        
        print(f"信号统计:")
        print(f"  - 均值: {signal_mean:.6f} V")
        print(f"  - 标准差: {signal_std:.6f} V")
        print(f"  - RMS: {signal_rms:.6f} V")
        print(f"  - 检测频率: {detected_freq:.2f} Hz")
        print(f"  - 检测幅值: {detected_amp:.4f} V")
        
        # 基本质量检查
        assert abs(signal_mean) < 0.1, f"信号直流偏移过大: {signal_mean:.6f} V"
        assert detected_amp > 0.1, f"检测到的信号幅值过小: {detected_amp:.4f} V"
        
        print("✓ 信号质量检查通过")
        
    except Exception as e:
        pytest.fail(f"信号质量测试失败: {e}")


if __name__ == "__main__":
    """
    直接运行测试（不使用pytest）
    """
    print("开始测试AI-AO集成功能...")
    print("警告：某些测试需要手动连接AO和AI通道进行环回测试")
    
    try:
        test_ai_ao_loopback_basic()
        test_ai_ao_different_channels()
        test_ai_ao_signal_quality()
        print("\n=== 所有集成测试完成 ===")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        raise
