"""
# 数据采集模块

模块路径：`sweeper400.measure.draft`

本模块包含NI数据采集卡相关的功能实现。
主要包含AI任务创建和数据采集功能。
"""

import numpy as np
import nidaqmx
from nidaqmx.constants import AcquisitionType, READ_ALL_AVAILABLE
from typing import Union, List, Tuple
from sweeper400.logger import get_logger
from sweeper400.analyze import (
    SamplingInfo,
    Waveform,
    sine_wave_vvi,
    extract_single_tone_information_vvi,
    PositiveFloat,
)

# 获取模块日志器
logger = get_logger(__name__)


def finite_ai_single_channel(channel: str, sampling_info: SamplingInfo) -> Waveform:
    """
    创建有限采样单通道AI任务并返回测量数据

    该函数创建一个有限采样的单通道模拟输入任务，使用机箱的PXIe_CLK100时钟作为参考时钟，
    并返回封装为Waveform对象的测量数据。

    Args:
        channel: AI任务的通道名称，例如 "402Dev2Slot2/ai0"
        sampling_info: 标准化的采样信息字典，包含采样率和采样数

    Returns:
        Waveform: 包含测量数据和元数据的波形对象

    Raises:
        ValueError: 当采样参数无效时
        RuntimeError: 当数据采集失败时

    Examples:
        ```python
        from sweeper400.analyze import init_sampling_info
        from sweeper400.measure.draft import finite_ai_single_channel

        # 创建采样信息
        sampling_info = init_sampling_info(1000, 2048)

        # 执行数据采集
        waveform = finite_ai_single_channel("402Dev2Slot2/ai0", sampling_info)

        print(f"采集到 {waveform.samples_num} 个样本")
        print(f"采样率: {waveform.sampling_rate} Hz")
        ```
    """
    logger.info(f"开始创建AI任务 - 通道: {channel}")
    logger.debug(
        f"采样参数 - 采样率: {sampling_info['sampling_rate']} Hz, "
        f"采样数: {sampling_info['samples_num']}"
    )

    try:
        # 创建AI任务
        with nidaqmx.Task() as task:
            # 添加AI电压通道，电压范围设置为±10V
            task.ai_channels.add_ai_voltage_chan(channel, min_val=-10.0, max_val=10.0)
            logger.debug(f"已添加AI通道: {channel}, 电压范围: ±10.0V")

            # 配置采样时钟 - 有限采样模式
            task.timing.cfg_samp_clk_timing(
                rate=sampling_info["sampling_rate"],
                sample_mode=AcquisitionType.FINITE,
                samps_per_chan=sampling_info["samples_num"],
            )
            logger.debug(f"已配置采样时钟 - 有限采样模式")

            # 设置参考时钟为PXIe_CLK100
            task.timing.ref_clk_src = "PXIe_Clk100"
            task.timing.ref_clk_rate = 100000000  # 100 MHz
            logger.debug("已设置参考时钟: PXIe_Clk100 (100 MHz)")

            # 启动任务
            task.start()
            logger.debug("AI任务已启动")

            # 读取所有可用数据
            data = task.read(READ_ALL_AVAILABLE)
            logger.info(f"成功读取 {len(data)} 个数据点")

            # 停止任务
            task.stop()
            logger.debug("AI任务已停止")

        # 将数据转换为numpy数组
        data_array = np.array(data, dtype=np.float64)
        logger.debug(f"数据转换完成 - 数组形状: {data_array.shape}")

        # 创建Waveform对象（timestamp参数设为None，将使用当前时间）
        waveform = Waveform(
            data_array, sampling_rate=sampling_info["sampling_rate"], timestamp=None
        )
        logger.info(f"成功创建Waveform对象 - {waveform}")

        return waveform

    except Exception as e:
        logger.error(f"AI任务执行失败: {e}")
        raise RuntimeError(f"数据采集失败: {e}") from e


def finite_ao_single_channel(channel: str, waveform: Waveform) -> int:
    """
    创建有限采样单通道AO任务并输出波形数据

    该函数接收Waveform对象，创建一个有限采样的单通道模拟输出任务，使用机箱的PXIe_CLK100时钟作为参考时钟，
    并将Waveform中的数据通过AO任务输出。

    Args:
        channel: AO任务的通道名称，例如 "402Dev2Slot2/ao0"
        waveform: 包含输出数据和采样信息的Waveform对象

    Returns:
        int: 成功写入的样本数

    Raises:
        ValueError: 当波形参数无效时
        RuntimeError: 当数据输出失败时

    Examples:
        ```python
        from sweeper400.analyze import init_sampling_info, sine_wave_vvi
        from sweeper400.measure.draft import finite_ao_single_channel

        # 创建测试波形
        sampling_info = init_sampling_info(1000, 2048)
        test_waveform = sine_wave_vvi(sampling_info, frequency=100.0, amplitude=1.0)

        # 执行数据输出
        samples_written = finite_ao_single_channel("402Dev2Slot2/ao0", test_waveform)

        print(f"成功输出 {samples_written} 个样本")
        ```
    """
    logger.info(f"开始创建AO任务 - 通道: {channel}")
    logger.debug(
        f"波形参数 - 采样率: {waveform.sampling_rate} Hz, "
        f"采样数: {waveform.samples_num}, 形状: {waveform.shape}"
    )

    # 验证波形数据
    if waveform.ndim == 2:
        if waveform.channels_num > 1:
            logger.warning("检测到多通道波形，只使用第一个通道")
            output_data = waveform[0, :].copy()
        else:
            output_data = waveform[0, :].copy()
    else:
        output_data = waveform.copy()

    # 验证数据范围（±10V安全限制）
    data_min, data_max = np.min(output_data), np.max(output_data)
    if data_min < -10.0 or data_max > 10.0:
        logger.error(f"波形数据超出±10V安全范围: [{data_min:.6f}, {data_max:.6f}] V")
        raise ValueError(
            f"波形数据超出±10V安全范围: [{data_min:.6f}, {data_max:.6f}] V"
        )

    logger.debug(f"数据范围检查通过: [{data_min:.6f}, {data_max:.6f}] V")

    try:
        # 创建AO任务
        with nidaqmx.Task() as task:
            # 添加AO电压通道，电压范围设置为±10V
            task.ao_channels.add_ao_voltage_chan(channel, min_val=-10.0, max_val=10.0)
            logger.debug(f"已添加AO通道: {channel}, 电压范围: ±10.0V")

            # 配置采样时钟 - 有限采样模式
            task.timing.cfg_samp_clk_timing(
                rate=waveform.sampling_rate,
                sample_mode=AcquisitionType.FINITE,
                samps_per_chan=waveform.samples_num,
            )
            logger.debug("已配置采样时钟 - 有限采样模式")

            # 设置参考时钟为PXIe_CLK100
            task.timing.ref_clk_src = "PXIe_Clk100"
            task.timing.ref_clk_rate = 100000000  # 100 MHz
            logger.debug("已设置参考时钟: PXIe_Clk100 (100 MHz)")

            # 写入数据到缓冲区（不自动启动）
            samples_written = task.write(output_data.tolist(), auto_start=False)
            logger.debug(f"已写入 {samples_written} 个样本到缓冲区")

            # 启动任务
            task.start()
            logger.debug("AO任务已启动")

            # 等待任务完成
            task.wait_until_done()
            logger.debug("AO任务执行完成")

            # 停止任务
            task.stop()
            logger.debug("AO任务已停止")

        logger.info(f"成功输出 {samples_written} 个数据点")
        return samples_written

    except Exception as e:
        logger.error(f"AO任务执行失败: {e}")
        raise RuntimeError(f"数据输出失败: {e}") from e


def get_terminal_name_with_dev_prefix(task: nidaqmx.Task, terminal_name: str) -> str:
    """
    获取带设备前缀的终端名称

    Args:
        task: 指定要获取设备名称的任务
        terminal_name: 指定要获取的终端名称

    Returns:
        带设备前缀的终端名称

    Raises:
        RuntimeError: 当在任务中找不到合适的设备时
    """
    from nidaqmx.constants import ProductCategory

    for device in task.devices:
        if device.product_category not in [
            ProductCategory.C_SERIES_MODULE,
            ProductCategory.SCXI_MODULE,
        ]:
            terminal_name_with_prefix = f"/{device.name}/{terminal_name}"
            logger.debug(f"获取终端名称: {terminal_name_with_prefix}")
            return terminal_name_with_prefix

    raise RuntimeError("在任务中找不到合适的设备")


def synchronized_ai_ao_measurement(
    ai_channel: str,
    ao_channel: str,
    sampling_info: SamplingInfo,
    frequency: PositiveFloat,
    amplitude: PositiveFloat,
    phase: float = 0.0,
) -> Tuple[PositiveFloat, PositiveFloat, float]:
    """
    创建同步的AI-AO测量任务

    该函数创建一对参数相同的有限长单通道AI和AO任务，两任务均使用PXIe_CLK100时钟，
    且通过触发器严格同步开始。AO任务输出指定参数的正弦波，AI任务同步采集数据。
    采集完成后，取AI数据的后半部分进行单频信息提取。

    Args:
        ai_channel: AI任务的通道名称，例如 "402Dev2Slot2/ai0"
        ao_channel: AO任务的通道名称，例如 "402Dev2Slot2/ao0"
        sampling_info: 标准化的采样信息字典，包含采样率和采样数
        frequency: AO输出正弦波的频率（Hz）
        amplitude: AO输出正弦波的幅值（V）
        phase: AO输出正弦波的相位（弧度），默认为0.0

    Returns:
        Tuple[PositiveFloat, PositiveFloat, float]: 检测到的频率、幅值、相位

    Raises:
        ValueError: 当参数无效时
        RuntimeError: 当测量失败时

    Examples:
        ```python
        from sweeper400.analyze import init_sampling_info
        from sweeper400.measure.draft import synchronized_ai_ao_measurement

        # 创建采样信息
        sampling_info = init_sampling_info(2000, 4096)

        # 执行同步测量
        freq, amp, phase = synchronized_ai_ao_measurement(
            "402Dev2Slot2/ai0", "402Dev2Slot2/ao0",
            sampling_info, frequency=100.0, amplitude=1.0
        )

        print(f"检测结果: {freq:.2f} Hz, {amp:.4f} V, {phase:.4f} rad")
        ```
    """
    logger.info(f"开始同步AI-AO测量 - AI: {ai_channel}, AO: {ao_channel}")
    logger.debug(
        f"测量参数 - 采样率: {sampling_info['sampling_rate']} Hz, "
        f"采样数: {sampling_info['samples_num']}, "
        f"信号: {frequency} Hz, {amplitude} V, {phase:.4f} rad"
    )

    # 验证幅值范围
    if amplitude > 10.0:
        logger.error(f"信号幅值超出±10V安全范围: {amplitude} V")
        raise ValueError(f"信号幅值超出±10V安全范围: {amplitude} V")

    try:
        # 生成AO输出波形
        ao_waveform = sine_wave_vvi(
            sampling_info, frequency=frequency, amplitude=amplitude, phase=phase
        )
        logger.debug(f"生成AO波形: {ao_waveform}")

        # 创建AI和AO任务
        with nidaqmx.Task() as ai_task, nidaqmx.Task() as ao_task:
            # 配置AI任务
            ai_task.ai_channels.add_ai_voltage_chan(
                ai_channel, min_val=-10.0, max_val=10.0
            )
            ai_task.timing.cfg_samp_clk_timing(
                rate=sampling_info["sampling_rate"],
                sample_mode=AcquisitionType.FINITE,
                samps_per_chan=sampling_info["samples_num"],
            )
            ai_task.timing.ref_clk_src = "PXIe_Clk100"
            ai_task.timing.ref_clk_rate = 100000000
            logger.debug("AI任务配置完成")

            # 配置AO任务
            ao_task.ao_channels.add_ao_voltage_chan(
                ao_channel, min_val=-10.0, max_val=10.0
            )
            ao_task.timing.cfg_samp_clk_timing(
                rate=sampling_info["sampling_rate"],
                sample_mode=AcquisitionType.FINITE,
                samps_per_chan=sampling_info["samples_num"],
            )
            ao_task.timing.ref_clk_src = "PXIe_Clk100"
            ao_task.timing.ref_clk_rate = 100000000
            logger.debug("AO任务配置完成")

            # 使用软件同步 - 两个任务都使用相同的PXIe_CLK100参考时钟
            # 这样可以确保时钟同步，虽然启动时间可能有微小差异
            logger.debug("使用软件同步方式（共享PXIe_CLK100参考时钟）")

            # 写入AO数据到缓冲区
            ao_task.write(ao_waveform.tolist(), auto_start=False)
            logger.debug("AO数据已写入缓冲区")

            # 启动任务 - 软件同步：几乎同时启动两个任务
            # 由于都使用相同的PXIe_CLK100参考时钟，时钟是同步的
            ao_task.start()
            ai_task.start()
            logger.debug("AI和AO任务已启动（软件同步，共享参考时钟）")

            # 读取AI数据
            ai_data = ai_task.read(READ_ALL_AVAILABLE)
            logger.info(f"成功采集 {len(ai_data)} 个AI数据点")

            # 等待AO任务完成
            ao_task.wait_until_done()
            logger.debug("AO任务执行完成")

            # 停止任务
            ai_task.stop()
            ao_task.stop()
            logger.debug("AI和AO任务已停止")

        # 处理AI数据 - 取后半部分
        total_samples = len(ai_data)
        half_point = total_samples // 2
        second_half_data = ai_data[half_point:]
        logger.debug(
            f"提取后半部分数据: {len(second_half_data)} 个样本 (从第 {half_point+1} 个开始)"
        )

        # 创建Waveform对象用于分析
        analysis_waveform = Waveform(
            np.array(second_half_data, dtype=np.float64),
            sampling_rate=sampling_info["sampling_rate"],
            timestamp=None,
        )
        logger.debug(f"创建分析波形: {analysis_waveform}")

        # 提取单频信息
        detected_freq, detected_amp, detected_phase = (
            extract_single_tone_information_vvi(
                analysis_waveform, approx_freq=frequency
            )
        )

        logger.info(
            f"检测结果 - 频率: {detected_freq:.2f} Hz, "
            f"幅值: {detected_amp:.4f} V, 相位: {detected_phase:.4f} rad"
        )

        return detected_freq, detected_amp, detected_phase

    except Exception as e:
        logger.error(f"同步AI-AO测量失败: {e}")
        raise RuntimeError(f"同步测量失败: {e}") from e
