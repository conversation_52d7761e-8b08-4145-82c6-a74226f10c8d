"""
# 数据采集模块

模块路径：`sweeper400.measure.draft`

本模块包含NI数据采集卡相关的功能实现。
主要包含AI任务创建和数据采集功能。
"""

import numpy as np
import nidaqmx
from nidaqmx.constants import AcquisitionType, READ_ALL_AVAILABLE
from typing import Union, List
from sweeper400.logger import get_logger
from sweeper400.analyze import SamplingInfo, Waveform

# 获取模块日志器
logger = get_logger(__name__)


def finite_ai_single_channel(channel: str, sampling_info: SamplingInfo) -> Waveform:
    """
    创建有限采样单通道AI任务并返回测量数据

    该函数创建一个有限采样的单通道模拟输入任务，使用机箱的PXIe_CLK100时钟作为参考时钟，
    并返回封装为Waveform对象的测量数据。

    Args:
        channel: AI任务的通道名称，例如 "402Dev2Slot2/ai0"
        sampling_info: 标准化的采样信息字典，包含采样率和采样数

    Returns:
        Waveform: 包含测量数据和元数据的波形对象

    Raises:
        ValueError: 当采样参数无效时
        RuntimeError: 当数据采集失败时

    Examples:
        ```python
        from sweeper400.analyze import init_sampling_info
        from sweeper400.measure.draft import finite_ai_single_channel

        # 创建采样信息
        sampling_info = init_sampling_info(1000, 2048)

        # 执行数据采集
        waveform = finite_ai_single_channel("402Dev2Slot2/ai0", sampling_info)

        print(f"采集到 {waveform.samples_num} 个样本")
        print(f"采样率: {waveform.sampling_rate} Hz")
        ```
    """
    logger.info(f"开始创建AI任务 - 通道: {channel}")
    logger.debug(
        f"采样参数 - 采样率: {sampling_info['sampling_rate']} Hz, "
        f"采样数: {sampling_info['samples_num']}"
    )

    try:
        # 创建AI任务
        with nidaqmx.Task() as task:
            # 添加AI电压通道，电压范围设置为±10V
            task.ai_channels.add_ai_voltage_chan(channel, min_val=-10.0, max_val=10.0)
            logger.debug(f"已添加AI通道: {channel}, 电压范围: ±10.0V")

            # 配置采样时钟 - 有限采样模式
            task.timing.cfg_samp_clk_timing(
                rate=sampling_info["sampling_rate"],
                sample_mode=AcquisitionType.FINITE,
                samps_per_chan=sampling_info["samples_num"],
            )
            logger.debug(f"已配置采样时钟 - 有限采样模式")

            # 设置参考时钟为PXIe_CLK100
            task.timing.ref_clk_src = "PXIe_Clk100"
            task.timing.ref_clk_rate = 100000000  # 100 MHz
            logger.debug("已设置参考时钟: PXIe_Clk100 (100 MHz)")

            # 启动任务
            task.start()
            logger.debug("AI任务已启动")

            # 读取所有可用数据
            data = task.read(READ_ALL_AVAILABLE)
            logger.info(f"成功读取 {len(data)} 个数据点")

            # 停止任务
            task.stop()
            logger.debug("AI任务已停止")

        # 将数据转换为numpy数组
        data_array = np.array(data, dtype=np.float64)
        logger.debug(f"数据转换完成 - 数组形状: {data_array.shape}")

        # 创建Waveform对象（timestamp参数设为None，将使用当前时间）
        waveform = Waveform(
            data_array, sampling_rate=sampling_info["sampling_rate"], timestamp=None
        )
        logger.info(f"成功创建Waveform对象 - {waveform}")

        return waveform

    except Exception as e:
        logger.error(f"AI任务执行失败: {e}")
        raise RuntimeError(f"数据采集失败: {e}") from e
