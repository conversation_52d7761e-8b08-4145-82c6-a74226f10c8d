"""
测试有限采样单通道AI任务功能

本测试文件用于测试 sweeper400.measure.draft.finite_ai_single_channel 函数的功能。
"""

import pytest
import numpy as np
from sweeper400.analyze import init_sampling_info, Waveform
from sweeper400.measure.draft import finite_ai_single_channel


def test_finite_ai_single_channel_basic():
    """
    测试基本的有限采样单通道AI任务功能
    
    该测试使用实际的NI硬件进行数据采集测试。
    """
    print("\n=== 测试有限采样单通道AI任务 ===")
    
    # 创建采样信息
    sampling_rate = 1000  # 1 kHz
    samples_num = 1024    # 1024个样本
    sampling_info = init_sampling_info(sampling_rate, samples_num)
    
    print(f"采样参数: {sampling_info}")
    
    # 测试通道 - 使用第一张板卡的第一个通道
    channel = "402Dev2Slot2/ai0"
    print(f"测试通道: {channel}")
    
    try:
        # 执行数据采集
        waveform = finite_ai_single_channel(channel, sampling_info)
        
        # 验证返回的数据类型
        assert isinstance(waveform, Waveform), "返回值应该是Waveform对象"
        
        # 验证数据形状
        assert waveform.samples_num == samples_num, f"采样数不匹配: 期望{samples_num}, 实际{waveform.samples_num}"
        assert waveform.sampling_rate == sampling_rate, f"采样率不匹配: 期望{sampling_rate}, 实际{waveform.sampling_rate}"
        
        # 验证数据类型
        assert waveform.dtype == np.float64, "数据类型应该是float64"
        
        # 验证数据范围（应该在±10V范围内）
        assert np.all(waveform >= -10.0), "数据值不应小于-10V"
        assert np.all(waveform <= 10.0), "数据值不应大于10V"
        
        print(f"✓ 成功采集数据: {waveform}")
        print(f"  - 数据形状: {waveform.shape}")
        print(f"  - 数据范围: [{np.min(waveform):.6f}, {np.max(waveform):.6f}] V")
        print(f"  - 数据均值: {np.mean(waveform):.6f} V")
        print(f"  - 数据标准差: {np.std(waveform):.6f} V")
        
    except Exception as e:
        pytest.fail(f"数据采集失败: {e}")


def test_finite_ai_single_channel_different_params():
    """
    测试不同采样参数的AI任务
    """
    print("\n=== 测试不同采样参数 ===")
    
    # 测试参数列表
    test_params = [
        (500, 512),    # 500 Hz, 512 samples
        (2000, 2048),  # 2 kHz, 2048 samples
        (5000, 1000),  # 5 kHz, 1000 samples
    ]
    
    channel = "402Dev2Slot2/ai0"
    
    for sampling_rate, samples_num in test_params:
        print(f"\n测试参数: {sampling_rate} Hz, {samples_num} samples")
        
        sampling_info = init_sampling_info(sampling_rate, samples_num)
        
        try:
            waveform = finite_ai_single_channel(channel, sampling_info)
            
            # 验证基本属性
            assert waveform.samples_num == samples_num
            assert waveform.sampling_rate == sampling_rate
            
            print(f"✓ 成功 - 持续时间: {waveform.duration:.6f} s")
            
        except Exception as e:
            pytest.fail(f"参数 ({sampling_rate}, {samples_num}) 测试失败: {e}")


def test_finite_ai_single_channel_multiple_channels():
    """
    测试不同通道的AI任务
    """
    print("\n=== 测试不同通道 ===")
    
    # 测试通道列表
    test_channels = [
        "402Dev2Slot2/ai0",
        "402Dev2Slot2/ai1", 
        "402Dev2Slot3/ai0",
        "402Dev2Slot3/ai1",
    ]
    
    sampling_info = init_sampling_info(1000, 512)
    
    for channel in test_channels:
        print(f"\n测试通道: {channel}")
        
        try:
            waveform = finite_ai_single_channel(channel, sampling_info)
            
            # 验证基本属性
            assert isinstance(waveform, Waveform)
            assert waveform.samples_num == 512
            assert waveform.sampling_rate == 1000
            
            print(f"✓ 成功 - 数据范围: [{np.min(waveform):.6f}, {np.max(waveform):.6f}] V")
            
        except Exception as e:
            print(f"✗ 通道 {channel} 测试失败: {e}")
            # 不使用pytest.fail，因为某些通道可能没有连接信号


if __name__ == "__main__":
    """
    直接运行测试（不使用pytest）
    """
    print("开始测试有限采样单通道AI任务功能...")
    
    try:
        test_finite_ai_single_channel_basic()
        test_finite_ai_single_channel_different_params()
        test_finite_ai_single_channel_multiple_channels()
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        raise
