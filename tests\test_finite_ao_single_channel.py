"""
测试有限采样单通道AO任务功能

本测试文件用于测试 sweeper400.measure.draft.finite_ao_single_channel 函数的功能。
"""

import pytest
import numpy as np
from sweeper400.analyze import init_sampling_info, sine_wave_vvi, Waveform
from sweeper400.measure.draft import finite_ao_single_channel


def test_finite_ao_single_channel_basic():
    """
    测试基本的有限采样单通道AO任务功能
    
    该测试使用实际的NI硬件进行数据输出测试。
    """
    print("\n=== 测试有限采样单通道AO任务 ===")
    
    # 创建测试波形
    sampling_rate = 1000  # 1 kHz
    samples_num = 1024    # 1024个样本
    sampling_info = init_sampling_info(sampling_rate, samples_num)
    
    # 生成正弦波测试信号
    frequency = 100.0     # 100 Hz
    amplitude = 2.0       # 2V幅值（在±10V范围内安全）
    test_waveform = sine_wave_vvi(sampling_info, frequency, amplitude)
    
    print(f"测试波形: {test_waveform}")
    print(f"数据范围: [{np.min(test_waveform):.6f}, {np.max(test_waveform):.6f}] V")
    
    # 测试通道 - 使用第一张板卡的第一个AO通道
    channel = "402Dev2Slot2/ao0"
    print(f"测试通道: {channel}")
    
    try:
        # 执行数据输出
        samples_written = finite_ao_single_channel(channel, test_waveform)
        
        # 验证返回值
        assert isinstance(samples_written, int), "返回值应该是整数"
        assert samples_written == samples_num, f"写入样本数不匹配: 期望{samples_num}, 实际{samples_written}"
        
        print(f"✓ 成功输出数据: {samples_written} 个样本")
        print(f"  - 波形频率: {frequency} Hz")
        print(f"  - 波形幅值: {amplitude} V")
        print(f"  - 持续时间: {test_waveform.duration:.6f} s")
        
    except Exception as e:
        pytest.fail(f"数据输出失败: {e}")


def test_finite_ao_single_channel_different_waveforms():
    """
    测试不同波形的AO任务
    """
    print("\n=== 测试不同波形参数 ===")
    
    # 测试参数列表
    test_params = [
        (500, 512, 50.0, 1.0),     # 500 Hz采样, 50 Hz信号, 1V幅值
        (2000, 2048, 200.0, 3.0),  # 2 kHz采样, 200 Hz信号, 3V幅值
        (5000, 1000, 500.0, 0.5),  # 5 kHz采样, 500 Hz信号, 0.5V幅值
    ]
    
    channel = "402Dev2Slot2/ao0"
    
    for sampling_rate, samples_num, signal_freq, amplitude in test_params:
        print(f"\n测试参数: {sampling_rate} Hz采样, {signal_freq} Hz信号, {amplitude} V幅值")
        
        sampling_info = init_sampling_info(sampling_rate, samples_num)
        test_waveform = sine_wave_vvi(sampling_info, signal_freq, amplitude)
        
        try:
            samples_written = finite_ao_single_channel(channel, test_waveform)
            
            # 验证基本属性
            assert samples_written == samples_num
            
            print(f"✓ 成功 - 输出 {samples_written} 个样本, 持续时间: {test_waveform.duration:.6f} s")
            
        except Exception as e:
            pytest.fail(f"参数 ({sampling_rate}, {samples_num}, {signal_freq}, {amplitude}) 测试失败: {e}")


def test_finite_ao_single_channel_multiple_channels():
    """
    测试不同AO通道的输出任务
    """
    print("\n=== 测试不同AO通道 ===")
    
    # 测试通道列表
    test_channels = [
        "402Dev2Slot2/ao0",
        "402Dev2Slot2/ao1", 
        "402Dev2Slot3/ao0",
        "402Dev2Slot3/ao1",
    ]
    
    sampling_info = init_sampling_info(1000, 512)
    
    for i, channel in enumerate(test_channels):
        print(f"\n测试通道: {channel}")
        
        # 为每个通道生成不同频率的测试信号
        frequency = 50.0 + i * 25.0  # 50, 75, 100, 125 Hz
        amplitude = 1.0 + i * 0.5    # 1.0, 1.5, 2.0, 2.5 V
        test_waveform = sine_wave_vvi(sampling_info, frequency, amplitude)
        
        try:
            samples_written = finite_ao_single_channel(channel, test_waveform)
            
            # 验证基本属性
            assert isinstance(samples_written, int)
            assert samples_written == 512
            
            print(f"✓ 成功 - {frequency} Hz, {amplitude} V, {samples_written} 样本")
            
        except Exception as e:
            print(f"✗ 通道 {channel} 测试失败: {e}")
            # 不使用pytest.fail，因为某些通道可能有硬件限制


def test_finite_ao_single_channel_multi_channel_waveform():
    """
    测试多通道Waveform的处理（应该只使用第一个通道）
    """
    print("\n=== 测试多通道Waveform处理 ===")
    
    sampling_info = init_sampling_info(1000, 512)
    
    # 创建单通道波形
    single_wave = sine_wave_vvi(sampling_info, 100.0, 1.5)
    
    # 创建多通道波形数据
    multi_channel_data = np.array([
        single_wave,                                    # 第一通道：目标信号
        np.random.normal(0, 0.1, single_wave.shape[0]) # 第二通道：噪声
    ])
    
    # 创建多通道Waveform对象
    multi_waveform = Waveform(
        multi_channel_data, 
        sampling_rate=sampling_info["sampling_rate"],
        timestamp=single_wave.timestamp
    )
    
    print(f"多通道波形: {multi_waveform}")
    
    channel = "402Dev2Slot2/ao0"
    
    try:
        samples_written = finite_ao_single_channel(channel, multi_waveform)
        
        # 验证结果
        assert samples_written == 512
        
        print(f"✓ 成功处理多通道波形 - 输出 {samples_written} 个样本")
        
    except Exception as e:
        pytest.fail(f"多通道波形测试失败: {e}")


def test_finite_ao_single_channel_voltage_range_check():
    """
    测试电压范围检查功能
    """
    print("\n=== 测试电压范围检查 ===")
    
    sampling_info = init_sampling_info(1000, 100)
    
    # 测试超出范围的波形
    dangerous_data = np.array([12.0] * 100)  # 超出+10V限制
    dangerous_waveform = Waveform(
        dangerous_data,
        sampling_rate=sampling_info["sampling_rate"],
        timestamp=None
    )
    
    channel = "402Dev2Slot2/ao0"
    
    # 应该抛出ValueError异常
    with pytest.raises(ValueError, match="波形数据超出±10V安全范围"):
        finite_ao_single_channel(channel, dangerous_waveform)
    
    print("✓ 电压范围检查正常工作")


if __name__ == "__main__":
    """
    直接运行测试（不使用pytest）
    """
    print("开始测试有限采样单通道AO任务功能...")
    
    try:
        test_finite_ao_single_channel_basic()
        test_finite_ao_single_channel_different_waveforms()
        test_finite_ao_single_channel_multiple_channels()
        test_finite_ao_single_channel_multi_channel_waveform()
        test_finite_ao_single_channel_voltage_range_check()
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        raise
