"""
测试同步AI-AO测量功能

本测试文件用于测试 sweeper400.measure.draft.synchronized_ai_ao_measurement 函数的功能。
"""

import pytest
import numpy as np
from sweeper400.analyze import init_sampling_info
from sweeper400.measure.draft import synchronized_ai_ao_measurement


def test_synchronized_ai_ao_measurement_basic():
    """
    测试基本的同步AI-AO测量功能
    
    该测试使用实际的NI硬件进行同步测量测试。
    注意：由于AI通道没有接信号源，检测不到正弦波是正常的。
    """
    print("\n=== 测试同步AI-AO测量功能 ===")
    print("注意：AI通道没有接信号源，检测不到正弦波是正常的")
    
    # 创建测试参数
    sampling_rate = 2000  # 2 kHz
    samples_num = 4096    # 4096个样本
    sampling_info = init_sampling_info(sampling_rate, samples_num)
    
    # 测试信号参数
    test_frequency = 100.0  # 100 Hz
    test_amplitude = 1.0    # 1V幅值
    test_phase = np.pi / 4  # 45度相位
    
    print(f"测试参数:")
    print(f"  - 采样率: {sampling_rate} Hz")
    print(f"  - 采样数: {samples_num}")
    print(f"  - 信号频率: {test_frequency} Hz")
    print(f"  - 信号幅值: {test_amplitude} V")
    print(f"  - 信号相位: {test_phase:.4f} rad")
    
    # 测试通道
    ai_channel = "402Dev2Slot2/ai0"
    ao_channel = "402Dev2Slot2/ao0"
    
    print(f"  - AI通道: {ai_channel}")
    print(f"  - AO通道: {ao_channel}")
    
    try:
        # 执行同步测量
        detected_freq, detected_amp, detected_phase = synchronized_ai_ao_measurement(
            ai_channel=ai_channel,
            ao_channel=ao_channel,
            sampling_info=sampling_info,
            frequency=test_frequency,
            amplitude=test_amplitude,
            phase=test_phase
        )
        
        # 验证返回值类型
        assert isinstance(detected_freq, (int, float)), "频率应该是数值类型"
        assert isinstance(detected_amp, (int, float)), "幅值应该是数值类型"
        assert isinstance(detected_phase, (int, float)), "相位应该是数值类型"
        
        print(f"\n检测结果:")
        print(f"  - 频率: {detected_freq:.2f} Hz")
        print(f"  - 幅值: {detected_amp:.4f} V")
        print(f"  - 相位: {detected_phase:.4f} rad")
        
        # 由于AI通道没有接信号源，检测到的幅值应该很小（噪声水平）
        print(f"\n验证结果:")
        if detected_amp < 0.1:
            print("✓ 检测到的幅值很小，符合无信号源的预期")
        else:
            print(f"⚠ 检测到较大幅值 {detected_amp:.4f} V，可能有信号源连接")
        
        print("✓ 同步AI-AO测量任务成功完成")
        
    except Exception as e:
        pytest.fail(f"同步AI-AO测量失败: {e}")


def test_synchronized_ai_ao_measurement_different_params():
    """
    测试不同参数的同步AI-AO测量
    """
    print("\n=== 测试不同参数的同步测量 ===")
    
    # 测试参数列表
    test_params = [
        (1000, 2048, 50.0, 0.5),    # 1 kHz采样, 50 Hz信号, 0.5V幅值
        (5000, 1024, 250.0, 2.0),   # 5 kHz采样, 250 Hz信号, 2.0V幅值
        (10000, 8192, 500.0, 1.5),  # 10 kHz采样, 500 Hz信号, 1.5V幅值
    ]
    
    ai_channel = "402Dev2Slot2/ai0"
    ao_channel = "402Dev2Slot2/ao0"
    
    for i, (sampling_rate, samples_num, frequency, amplitude) in enumerate(test_params):
        print(f"\n测试参数组 {i+1}: {sampling_rate} Hz采样, {frequency} Hz信号, {amplitude} V幅值")
        
        sampling_info = init_sampling_info(sampling_rate, samples_num)
        
        try:
            detected_freq, detected_amp, detected_phase = synchronized_ai_ao_measurement(
                ai_channel=ai_channel,
                ao_channel=ao_channel,
                sampling_info=sampling_info,
                frequency=frequency,
                amplitude=amplitude
            )
            
            # 验证基本属性
            assert isinstance(detected_freq, (int, float))
            assert isinstance(detected_amp, (int, float))
            assert isinstance(detected_phase, (int, float))
            
            print(f"✓ 成功 - 检测: {detected_freq:.2f} Hz, {detected_amp:.4f} V, {detected_phase:.4f} rad")
            
        except Exception as e:
            pytest.fail(f"参数组 {i+1} 测试失败: {e}")


def test_synchronized_ai_ao_measurement_different_channels():
    """
    测试不同通道组合的同步测量
    """
    print("\n=== 测试不同通道组合 ===")
    
    # 测试通道组合
    channel_pairs = [
        ("402Dev2Slot2/ai0", "402Dev2Slot2/ao0"),
        ("402Dev2Slot2/ai1", "402Dev2Slot2/ao1"),
        ("402Dev2Slot3/ai0", "402Dev2Slot3/ao0"),
        ("402Dev2Slot3/ai1", "402Dev2Slot3/ao1"),
    ]
    
    sampling_info = init_sampling_info(2000, 2048)
    
    for i, (ai_ch, ao_ch) in enumerate(channel_pairs):
        print(f"\n测试通道对 {i+1}: AI={ai_ch}, AO={ao_ch}")
        
        # 为每个通道对生成不同的测试信号
        frequency = 75.0 + i * 25.0   # 75, 100, 125, 150 Hz
        amplitude = 0.8 + i * 0.3     # 0.8, 1.1, 1.4, 1.7 V
        
        try:
            detected_freq, detected_amp, detected_phase = synchronized_ai_ao_measurement(
                ai_channel=ai_ch,
                ao_channel=ao_ch,
                sampling_info=sampling_info,
                frequency=frequency,
                amplitude=amplitude
            )
            
            # 验证基本属性
            assert isinstance(detected_freq, (int, float))
            assert isinstance(detected_amp, (int, float))
            assert isinstance(detected_phase, (int, float))
            
            print(f"✓ 成功 - {frequency} Hz, {amplitude} V -> {detected_freq:.2f} Hz, {detected_amp:.4f} V")
            
        except Exception as e:
            print(f"✗ 通道对 {ai_ch}-{ao_ch} 测试失败: {e}")
            # 不使用pytest.fail，因为某些通道可能有硬件限制


def test_synchronized_ai_ao_measurement_voltage_range_check():
    """
    测试电压范围检查功能
    """
    print("\n=== 测试电压范围检查 ===")
    
    sampling_info = init_sampling_info(1000, 1024)
    
    ai_channel = "402Dev2Slot2/ai0"
    ao_channel = "402Dev2Slot2/ao0"
    
    # 测试超出范围的幅值
    dangerous_amplitude = 12.0  # 超出±10V限制
    
    # 应该抛出ValueError异常
    with pytest.raises(ValueError, match="信号幅值超出±10V安全范围"):
        synchronized_ai_ao_measurement(
            ai_channel=ai_channel,
            ao_channel=ao_channel,
            sampling_info=sampling_info,
            frequency=100.0,
            amplitude=dangerous_amplitude
        )
    
    print("✓ 电压范围检查正常工作")


def test_synchronized_ai_ao_measurement_data_processing():
    """
    测试数据处理逻辑（后半部分提取）
    """
    print("\n=== 测试数据处理逻辑 ===")
    
    # 使用较大的采样数来验证后半部分提取
    sampling_info = init_sampling_info(4000, 8000)  # 8000个样本
    
    ai_channel = "402Dev2Slot2/ai0"
    ao_channel = "402Dev2Slot2/ao0"
    
    try:
        detected_freq, detected_amp, detected_phase = synchronized_ai_ao_measurement(
            ai_channel=ai_channel,
            ao_channel=ao_channel,
            sampling_info=sampling_info,
            frequency=200.0,
            amplitude=1.0
        )
        
        print(f"大数据量测试成功:")
        print(f"  - 总采样数: {sampling_info['samples_num']}")
        print(f"  - 分析样本数: {sampling_info['samples_num'] // 2} (后半部分)")
        print(f"  - 检测结果: {detected_freq:.2f} Hz, {detected_amp:.4f} V")
        
        print("✓ 数据处理逻辑正常工作")
        
    except Exception as e:
        pytest.fail(f"数据处理测试失败: {e}")


if __name__ == "__main__":
    """
    直接运行测试（不使用pytest）
    """
    print("开始测试同步AI-AO测量功能...")
    print("注意：AI通道没有接信号源，检测不到正弦波是正常的")
    
    try:
        test_synchronized_ai_ao_measurement_basic()
        test_synchronized_ai_ao_measurement_different_params()
        test_synchronized_ai_ao_measurement_different_channels()
        test_synchronized_ai_ao_measurement_voltage_range_check()
        test_synchronized_ai_ao_measurement_data_processing()
        print("\n=== 所有同步测量测试完成 ===")
        
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        raise
