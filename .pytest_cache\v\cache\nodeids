["tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_basic_extraction_perfect_sine", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_different_error_percentages", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_edge_cases", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_frequency_range_search", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_full_range_search", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_invalid_frequency_range", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_multi_channel_waveform", "tests/test_extract_single_tone_information_vvi.py::TestExtractSingleToneInformationVvi::test_noisy_signal", "tests/test_finite_ai_single_channel.py::test_finite_ai_single_channel_basic", "tests/test_finite_ai_single_channel.py::test_finite_ai_single_channel_different_params", "tests/test_finite_ai_single_channel.py::test_finite_ai_single_channel_multiple_channels", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_basic", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_different_waveforms", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_multi_channel_waveform", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_multiple_channels", "tests/test_finite_ao_single_channel.py::test_finite_ao_single_channel_voltage_range_check", "tests/test_sine_wave_vvi.py::test_sine_wave_vvi_basic", "tests/test_sine_wave_vvi.py::test_sine_wave_vvi_frequency_accuracy", "tests/test_sine_wave_vvi.py::test_sine_wave_vvi_with_amplitude", "tests/test_sine_wave_vvi.py::test_sine_wave_vvi_with_phase", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_continuous_generation_phase", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_continuous_generation_timestamp", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_different_amplitudes", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_different_frequencies", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_init_custom_parameters", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_init_default_parameters", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_phase_wrapping", "tests/test_sine_waveform_vvi.py::TestSineWaveformVvi::test_single_generation", "tests/test_synchronized_ai_ao_measurement.py::TestSynchronizedAiAoMeasurement::test_basic_measurement", "tests/test_synchronized_ai_ao_measurement.py::TestSynchronizedAiAoMeasurement::test_default_parameters", "tests/test_synchronized_ai_ao_measurement.py::TestSynchronizedAiAoMeasurement::test_different_parameters"]